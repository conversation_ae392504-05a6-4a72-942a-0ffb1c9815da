import "@/app/globals.css";
import React from "react";
import ClientBody from "./ClientBody";
import { AltoraLogo } from "../components/AltoraLogo";

const POLYSANS_FONT_URLS = [
  {
    url: "https://ext.same-assets.com/4123950039/3713092101.ttf",
    weight: "400",
    style: "normal",
  },
  {
    url: "https://ext.same-assets.com/4123950039/1287537253.ttf",
    weight: "500",
    style: "normal",
  },
  {
    url: "https://ext.same-assets.com/4123950039/523861748.ttf",
    weight: "700",
    style: "normal",
  },
];

export const metadata = {
  title: "Altora – AI Production Design Studio",
  description:
    "Professional design tools for TV series, movies, and entertainment productions. 110+ styles, authentic materials, and cinematic environments. Free to try.",
};

const NAV_LINKS = [
  { label: "Pricing", href: "/pricing" },
  { label: "Glossary", href: "/interior-design-glossary" },
  { label: "Advice", href: "/advice" },
  { label: "Podcast", href: "/podcast" },
];

function Header() {
  return (
    <header className="sticky top-0 z-30 w-full bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 backdrop-blur-lg">
      <nav className="flex items-center justify-between px-6 py-4 max-w-7xl mx-auto">
        <a href="/" className="flex items-center gap-2">
          <AltoraLogo size="md" />
        </a>
        <div className="flex gap-4 items-center">
          <a
            href="#pricing"
            className="text-gray-400 hover:text-white transition-colors text-sm font-medium"
          >
            Pricing
          </a>
          <a
            href="/start"
            className="text-gray-400 hover:text-white transition-colors text-sm font-medium"
          >
            Log in
          </a>
          <a
            href="/start"
            className="px-4 py-2 rounded bg-[#2dd4bf] text-black font-semibold hover:bg-[#14b8a6] transition-all text-sm"
          >
            Design your interior
          </a>
        </div>
      </nav>
    </header>
  );
}

function Footer() {
  return (
    <footer className="bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 border-t border-gray-800 mt-24">
      <div className="max-w-7xl mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Contact Column */}
          <div>
            <h3 className="text-white font-semibold mb-4">Contact</h3>
            <div className="space-y-3">
              <p className="text-gray-400"><EMAIL></p>
              <p className="text-gray-400">+1 (555) 123-4567</p>
              <div className="flex items-center gap-2 mt-4">
                <a
                  href="https://twitter.com/roomai"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>

          {/* Pages Column */}
          <div>
            <h3 className="text-white font-semibold mb-4">Pages</h3>
            <ul className="space-y-2">
              <li><a href="/signup" className="text-gray-400 hover:text-white transition-colors">Sign Up</a></li>
              <li><a href="/signin" className="text-gray-400 hover:text-white transition-colors">Sign In</a></li>
              <li><a href="/terms-of-service" className="text-gray-400 hover:text-white transition-colors">Terms of Service</a></li>
              <li><a href="/privacy-policy" className="text-gray-400 hover:text-white transition-colors">Privacy Policy</a></li>
              <li><a href="/faq" className="text-gray-400 hover:text-white transition-colors">FAQ</a></li>
            </ul>
          </div>

          {/* Links Column */}
          <div>
            <h3 className="text-white font-semibold mb-4">Links</h3>
            <ul className="space-y-2">
              <li><a href="#pricing" className="text-gray-400 hover:text-white transition-colors">Pricing</a></li>
              <li><a href="#features" className="text-gray-400 hover:text-white transition-colors">Features</a></li>
              <li><a href="#how-it-works" className="text-gray-400 hover:text-white transition-colors">How It Works</a></li>
              <li><a href="#testimonials" className="text-gray-400 hover:text-white transition-colors">Testimonials</a></li>
              <li><a href="#gallery" className="text-gray-400 hover:text-white transition-colors">Gallery</a></li>
            </ul>
          </div>
        </div>

        {/* Brand Section */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div className="mb-4 md:mb-0">
              <AltoraLogo size="md" />
              <p className="text-gray-400 mt-2 max-w-md text-sm">
                Transform your space with AI-powered interior design. Create stunning rooms in minutes.
              </p>
            </div>
            <p className="text-gray-400 text-sm">
              © 2024 RoomAI. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head />
      <body className="bg-gray-950 text-white font-sans min-h-screen flex flex-col">
        <Header />
        <ClientBody>{children}</ClientBody>
        <Footer />
      </body>
    </html>
  );
}
