"use client";
import React, { useState } from "react";
import HeroSection from "../components/HeroSection";
import { STYLES } from "../components/StyleSelector";

// Sample data for the new sections
const ROOM_TYPES = [
  { title: "Living Room", icon: "🛋️" },
  { title: "Bedroom", icon: "🛏️" },
  { title: "Kitchen", icon: "🍳" },
  { title: "Bathroom", icon: "🚿" },
  { title: "Dining Room", icon: "🍽️" },
  { title: "Office", icon: "💼" },
  { title: "Nursery", icon: "🍼" },
  { title: "Basement", icon: "🏠" }
];

const ALL_COLORS = [
  // Row 1 - Neutrals
  "#FFFFFF", "#F5F5F5", "#E8E8E8", "#D3D3D3", "#C0C0C0", "#A9A9A9", "#808080", "#696969", "#2F2F2F", "#000000",
  // Row 2 - Warm Colors
  "#FFE4E1", "#FFA07A", "#FF6347", "#FF4500", "#DC143C", "#B22222", "#8B0000", "#A0522D", "#D2691E", "#CD853F",
  // Row 3 - Cool Colors
  "#E0FFFF", "#AFEEEE", "#87CEEB", "#4682B4", "#1E90FF", "#0000FF", "#0000CD", "#00008B", "#191970", "#4B0082"
];

const MATERIALS = [
  { name: "Wood", icon: "🪵", types: ["Oak", "Pine", "Walnut", "Mahogany"] },
  { name: "Metal", icon: "⚙️", types: ["Steel", "Aluminum", "Brass", "Copper"] },
  { name: "Stone", icon: "🪨", types: ["Marble", "Granite", "Limestone", "Slate"] },
  { name: "Fabric", icon: "🧵", types: ["Cotton", "Linen", "Silk", "Wool"] },
  { name: "Glass", icon: "🔍", types: ["Clear", "Frosted", "Tinted", "Textured"] },
  { name: "Ceramic", icon: "🏺", types: ["Porcelain", "Terracotta", "Stoneware", "Earthenware"] }
];

export default function HomePage() {
  const [isYearly, setIsYearly] = useState(false);

  return (
    <>
      {/* Hero Section */}
      <HeroSection
        title="Professional Production Design"
        subtitle="Create authentic sets for TV series, movies, and entertainment productions. Professional design tools with 110+ styles and cinematic environments."
        primaryButtonText="Design your interior"
        primaryButtonHref="/design/new"
        secondaryButtonText="See pricing"
        secondaryButtonHref="/pricing"
      />

      {/* Inspiration Sections */}
      <section className="w-full bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-16">
        <div className="max-w-7xl mx-auto px-6">
          {/* Rooms Section */}
          <div className="mb-16">
            <div className="bg-gray-900 rounded-2xl p-8 border border-gray-800">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-white">Rooms</h2>
              </div>
              <p className="text-gray-400 mb-6">No matter what type of room you're designing, we've got you covered.</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {ROOM_TYPES.map((room) => (
                  <div key={room.title} className="group cursor-pointer">
                    <div className="relative overflow-hidden rounded-lg bg-gray-800 aspect-square">
                      <img
                        src={room.image}
                        alt={room.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300" />
                    </div>
                    <h3 className="text-white font-medium mt-2 text-center">{room.title}</h3>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Colors Section */}
          <div className="mb-16">
            <div className="bg-gray-900 rounded-2xl p-8 border border-gray-800">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-white">Colors</h2>
              </div>
              <p className="text-gray-400 mb-6">Choose your own colors, or have AI generate a color palette that matches your style and room.</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {COLOR_PALETTES.map((palette) => (
                  <div key={palette.name} className="group cursor-pointer">
                    <div className="flex gap-1 mb-2">
                      {palette.colors.map((color, colorIdx) => (
                        <div
                          key={colorIdx}
                          className="flex-1 h-16 rounded-lg border border-gray-700 group-hover:scale-105 transition-transform duration-300"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    <h3 className="text-white font-medium text-center">{palette.name}</h3>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Materials Section */}
          <div className="mb-16">
            <div className="bg-gray-900 rounded-2xl p-8 border border-gray-800">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-white">Materials</h2>
              </div>
              <p className="text-gray-400 mb-6">Try out dozens of materials to see how they would look in your interior.</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {MATERIALS.map((material) => (
                  <div key={material.name} className="group cursor-pointer">
                    <div className="relative overflow-hidden rounded-lg bg-gray-800 aspect-square">
                      <img
                        src={material.image}
                        alt={material.name}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300" />
                      <div className="absolute top-2 left-2">
                        <span className="bg-black/60 text-white text-xs px-2 py-1 rounded">{material.type}</span>
                      </div>
                    </div>
                    <h3 className="text-white font-medium mt-2 text-center">{material.name}</h3>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Styles Showcase Section */}
      <section className="w-full bg-gray-950 py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Choose from 50+ interior design styles
            </h2>
            <p className="text-gray-400 text-lg max-w-3xl mx-auto">
              With Interior AI's preset interior design styles, you don't need to do any of the hard work of writing prompts or setting parameters. Instead, with just one click, Interior AI designs with your style in mind.
            </p>
            <p className="text-teal-400 font-medium mt-4">
              All styles are included in your membership! You can try as many as you want.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {STYLES.slice(0, 18).map((style) => (
              <div key={style.id} className="group cursor-pointer bg-gray-900 rounded-xl overflow-hidden border border-gray-800 hover:border-teal-500 transition-all duration-300">
                <div className="relative aspect-video overflow-hidden">
                  <img
                    src={style.image}
                    alt={style.label}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300" />
                </div>
                <div className="p-4">
                  <h3 className="text-white font-semibold text-lg mb-2">{style.label}</h3>
                  <p className="text-gray-400 text-sm">{style.description}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <a
              href="/design/new"
              className="inline-flex items-center px-8 py-4 bg-teal-600 text-white font-semibold rounded-lg hover:bg-teal-500 transition-colors duration-300"
            >
              Explore All Styles
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </a>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="w-full bg-gradient-to-br from-gray-900 via-gray-950 to-black py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Simple, transparent pricing
            </h2>
            <p className="text-gray-400 text-lg">
              Choose the plan that's right for you
            </p>
          </div>

          {/* Pricing Toggle */}
          <div className="flex justify-center mb-12">
            <div className="bg-gray-800 p-1 rounded-lg">
              <button className="px-6 py-2 rounded-md bg-teal-600 text-white font-medium">
                Monthly
              </button>
              <button className="px-6 py-2 rounded-md text-gray-400 font-medium hover:text-white transition-colors">
                Yearly <span className="text-teal-400 text-sm">(33% off)</span>
              </button>
            </div>
          </div>

          {/* Pricing Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Personal Plan */}
            <div className="bg-gray-900 rounded-2xl p-8 border border-gray-800">
              <h3 className="text-xl font-bold text-white mb-2">Personal</h3>
              <p className="text-gray-400 text-sm mb-6">
                Discover your inner designer and create your dream home with our cost-effective, user-friendly software.
              </p>
              <div className="mb-6">
                <span className="text-4xl font-bold text-white">$29</span>
                <span className="text-gray-400">/month</span>
              </div>
              <button className="w-full bg-gray-800 text-white py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors mb-6">
                Start designing
              </button>
              <ul className="space-y-3 text-sm">
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  250 images per month
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Personal-use only
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Small watermark
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Normal resolution
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  1 user
                </li>
              </ul>
              <p className="text-gray-500 text-xs mt-4">Cancel anytime</p>
            </div>

            {/* Pro Plan */}
            <div className="bg-gray-900 rounded-2xl p-8 border-2 border-teal-500 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-teal-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                  Most popular
                </span>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Pro</h3>
              <p className="text-gray-400 text-sm mb-6">
                Brainstorm ideas quickly, impress clients with stunning visuals, and close deals faster using our professional tools.
              </p>
              <div className="mb-6">
                <span className="text-4xl font-bold text-white">$99</span>
                <span className="text-gray-400">/month</span>
              </div>
              <button className="w-full bg-teal-600 text-white py-3 rounded-lg font-medium hover:bg-teal-500 transition-colors mb-6">
                Start designing
              </button>
              <ul className="space-y-3 text-sm">
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  1,000 images per month
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Commercial license
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  No watermark
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Highest resolution
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  1 user
                </li>
              </ul>
              <p className="text-gray-500 text-xs mt-4">Cancel anytime</p>
            </div>

            {/* Team Plan */}
            <div className="bg-gray-900 rounded-2xl p-8 border border-gray-800">
              <h3 className="text-xl font-bold text-white mb-2">Team</h3>
              <p className="text-gray-400 text-sm mb-6">
                Empower your firm with cutting-edge AI technology to enhance efficiency and stay competitive in the market.
              </p>
              <div className="mb-6">
                <span className="text-4xl font-bold text-white">$299</span>
                <span className="text-gray-400">/month</span>
              </div>
              <button className="w-full bg-gray-800 text-white py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors mb-6">
                Start designing
              </button>
              <ul className="space-y-3 text-sm">
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  5,000 images per month
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Commercial license
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  No watermark
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Highest resolution
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Up to 5 users
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-4 h-4 text-teal-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Train your own style
                </li>
              </ul>
              <p className="text-gray-500 text-xs mt-4">Cancel anytime</p>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
